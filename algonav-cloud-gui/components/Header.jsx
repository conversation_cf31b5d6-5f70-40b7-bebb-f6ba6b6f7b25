'use client'

import React, { useState } from 'react';
import { App<PERSON>ar, Too<PERSON>bar, Typography, IconButton, Badge, Tooltip, Menu, MenuItem, ListItemIcon, ListItemText } from '@mui/material';
import NotificationsIcon from '@mui/icons-material/Notifications';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import SettingsIcon from '@mui/icons-material/Settings';
import LogoutIcon from '@mui/icons-material/Logout';
import HelpIcon from '@mui/icons-material/Help';
import Image from 'next/image';

const Header = () => {
  const [anchorEl, setAnchorEl] = useState(null);
  
  const handleMenu = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <AppBar position="fixed" sx={{ zIndex: (theme) => theme.zIndex.drawer + 1 }}>
      <Toolbar>
        <div style={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
          <Image
            src="/header_logo.png"
            alt="AlgoNav Logo"
            width={128}
            height={32}
            style={{ width: 'auto', height: '32px' }}
            priority
          />
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <Typography
              variant="h6"
              noWrap
              component="div"
              sx={{
                color: 'white',
                fontWeight: 500
              }}
            >
              Positioning Suite
            </Typography>
            <Typography
              variant="body2"
              noWrap
              component="div"
              sx={{
                color: 'rgba(255, 255, 255, 0.7)',
                fontWeight: 300,
                fontSize: '0.875rem',
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                px: 1.5,
                py: 0.5,
                borderRadius: '12px',
                border: '1px solid rgba(255, 255, 255, 0.2)'
              }}
            >
              LocalCloud
            </Typography>
          </div>
        </div>
        <div style={{ display: 'flex', gap: '12px' }}>
          <Tooltip title="Help">
            <IconButton color="inherit">
              <HelpIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Notifications">
            <IconButton color="inherit">
              <Badge badgeContent={2} color="secondary">
                <NotificationsIcon />
              </Badge>
            </IconButton>
          </Tooltip>
          <Tooltip title="Account">
            <IconButton
              color="inherit"
              onClick={handleMenu}
              aria-controls="account-menu"
              aria-haspopup="true"
            >
              <AccountCircleIcon />
            </IconButton>
          </Tooltip>
          <Menu
            id="account-menu"
            anchorEl={anchorEl}
            keepMounted
            open={Boolean(anchorEl)}
            onClose={handleClose}
          >
            <MenuItem onClick={handleClose}>
              <ListItemIcon>
                <AccountCircleIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Profile" />
            </MenuItem>
            <MenuItem onClick={handleClose}>
              <ListItemIcon>
                <SettingsIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Settings" />
            </MenuItem>
            <MenuItem onClick={handleClose}>
              <ListItemIcon>
                <LogoutIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Logout" />
            </MenuItem>
          </Menu>
        </div>
      </Toolbar>
    </AppBar>
  );
};

export default Header;